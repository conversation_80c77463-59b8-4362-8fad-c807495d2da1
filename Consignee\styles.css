/* consignee.css - Enhanced Dark Theme */
body {
    font-family: 'Inter', sans-serif;
    background-color: #121212;
    color: #ffffff;
    /* <PERSON>l<PERSON>'s min-h-screen and p-4/sm:p-6/md:p-8 are applied directly in HTML */
}

/* Custom scrollbar - Enhanced visibility */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}
::-webkit-scrollbar-track {
    background: #2a2a2a;
}
::-webkit-scrollbar-thumb {
    background: #666666;
    border-radius: 4px;
    border: 1px solid #555555;
}
::-webkit-scrollbar-thumb:hover {
    background: #888888;
    border-color: #777777;
}

/* Modal animations */
.modal-enter {
    opacity: 0;
    transform: scale(0.95) translateY(-20px);
}
.modal-enter-active {
    opacity: 1;
    transform: scale(1) translateY(0);
    transition: opacity 300ms ease-out, transform 300ms ease-out;
}
.modal-leave {
    opacity: 1;
    transform: scale(1) translateY(0);
}
.modal-leave-active {
    opacity: 0;
    transform: scale(0.95) translateY(-20px);
    transition: opacity 200ms ease-in, transform 200ms ease-in;
}

/* Table row selection */
.row-selected {
    background-color: #404040 !important; /* !important to override Tailwind hover if necessary */
}
.row-selected td {
    color: #fafafa;
}

/* Custom checkbox (Tailwind utility classes are powerful, but if specific overrides are needed) */
.custom-checkbox {
    appearance: none;
    -webkit-appearance: none;
    height: 1.25rem; /* h-5 */
    width: 1.25rem; /* w-5 */
    background-color: #262626; /* bg-neutral-800, slightly adjusted for contrast */
    border: 1px solid #525252; /* border-neutral-600 */
    border-radius: 0.25rem; /* rounded */
    display: inline-block;
    position: relative;
    cursor: pointer;
    transition: background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;
}
.custom-checkbox:checked {
    background-color: #a3a3a3; /* bg-neutral-400 */
    border-color: #d4d4d4; /* border-neutral-300 */
}
.custom-checkbox:checked::after {
    content: '';
    position: absolute;
    left: 0.375rem; /* Adjust for checkmark position */
    top: 0.125rem;  /* Adjust for checkmark position */
    width: 0.375rem; /* w-1.5 */
    height: 0.75rem; /* h-3 */
    border: solid #171717; /* Dark checkmark for contrast on light gray */
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

/* Sticky table header */
.table-header-sticky th {
    position: sticky;
    top: 0;
    z-index: 10; /* Ensure header is above table content */
    background-color: #171717; /* bg-neutral-900, very dark */
    border-bottom-width: 1px;
    border-color: #404040; /* border-neutral-700 */
}

/* Spinner for loading states - Enhanced visibility */
.spinner {
    border: 2px solid rgba(255, 255, 255, 0.2); /* Enhanced spinner base */
    border-radius: 50%;
    border-top-color: #ffffff; /* White spinner active part for better visibility */
    width: 1rem; /* w-4 */
    height: 1rem; /* h-4 */
    animation: spin 1s linear infinite;
    display: inline-block;
    margin-right: 0.5rem; /* mr-2 */
}
@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Sortable header styling - Enhanced visibility */
.sortable-header {
    cursor: pointer;
    user-select: none;
    color: #ffffff;
    font-weight: 600;
}
.sortable-header:hover {
    color: #e0e0e0; /* Enhanced hover visibility */
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.1);
}
.sort-indicator {
    margin-left: 0.5rem; /* ml-2 */
    font-size: 0.75rem; /* text-xs */
    color: #ffffff;
    font-weight: bold;
}

/* Toast Notification Styling */
#toast-container {
    position: fixed;
    top: 1.5rem; /* Adjust as needed */
    right: 1.5rem; /* Adjust as needed */
    z-index: 100; /* High z-index */
    display: flex;
    flex-direction: column;
    gap: 0.75rem; /* gap-3 */
}
.toast {
    display: flex;
    align-items: center;
    padding: 1rem; /* p-4 */
    border-radius: 0.5rem; /* rounded-lg */
    box-shadow: 0 10px 15px -3px rgba(255,255,255,0.1), 0 4px 6px -2px rgba(255,255,255,0.05); /* Enhanced shadow for better visibility */
    min-width: 250px;
    max-width: 350px;
    opacity: 0;
    transform: translateX(100%);
    transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
    background-color: #2a2a2a; /* Enhanced background */
    color: #ffffff; /* Pure white text for better contrast */
    border: 2px solid #666666; /* Enhanced border visibility */
}
.toast.show {
    opacity: 1;
    transform: translateX(0);
}
.toast-icon {
    margin-right: 0.75rem; /* mr-3 */
    font-size: 1.25rem; /* text-xl */
}
/* Icon colors for different toast types - Enhanced visibility */
.toast-success .toast-icon { color: #51cf66; /* Enhanced green for better contrast */ }
.toast-error .toast-icon { color: #ff6b6b; /* Enhanced red for better contrast */ }
.toast-info .toast-icon { color: #339af0; /* Enhanced blue for better contrast */ }
.toast-warning .toast-icon { color: #ffd43b; /* Enhanced yellow for better contrast */ }

/* Default consignee row highlight - Enhanced visibility */
.default-consignee-row td:first-child {
    border-left: 4px solid #ffffff; /* Enhanced white border for better visibility */
}
.default-consignee-row {
    background-color: rgba(255, 255, 255, 0.1); /* Enhanced background with white tint */
    border: 1px solid rgba(255, 255, 255, 0.2);
}
.default-consignee-row .fa-star {
    color: #ffd43b; /* Enhanced yellow star for better visibility */
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* ========================================
   ENHANCED DARK THEME SUPPORT
   ======================================== */

/* Enhanced table styling for better visibility */
table {
    background-color: #1e1e1e;
    border: 2px solid #444444;
    border-radius: 8px;
    overflow: hidden;
}

th {
    background-color: #2a2a2a;
    color: #ffffff;
    font-weight: 600;
    border-bottom: 2px solid #444444;
    padding: 1rem;
}

td {
    background-color: #1e1e1e;
    color: #e0e0e0;
    border-bottom: 1px solid #333333;
    padding: 0.75rem 1rem;
}

tr:hover td {
    background-color: #2a2a2a;
    color: #ffffff;
}

/* Enhanced button styling */
button {
    background-color: #2a2a2a;
    color: #ffffff;
    border: 2px solid #444444;
    border-radius: 6px;
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

button:hover {
    background-color: #3a3a3a;
    border-color: #666666;
    color: #ffffff;
    box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);
}

button:disabled {
    background-color: #1a1a1a;
    color: #666666;
    border-color: #333333;
    cursor: not-allowed;
}

/* Enhanced input styling */
input, select, textarea {
    background-color: #2a2a2a;
    color: #ffffff;
    border: 2px solid #444444;
    border-radius: 6px;
    padding: 0.5rem;
    transition: all 0.3s ease;
}

input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: #666666;
    background-color: #3a3a3a;
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

input::placeholder, textarea::placeholder {
    color: #b0b0b0;
    opacity: 1;
}

/* Enhanced modal styling */
.modal {
    background-color: rgba(0, 0, 0, 0.8);
}

.modal-content {
    background-color: #1e1e1e;
    border: 2px solid #444444;
    border-radius: 12px;
    color: #ffffff;
    box-shadow: 0 8px 32px rgba(255, 255, 255, 0.1);
}

/* Enhanced form styling */
.form-group label {
    color: #ffffff;
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: block;
}

.form-control {
    background-color: #2a2a2a;
    border: 2px solid #444444;
    color: #ffffff;
}

.form-control:focus {
    border-color: #666666;
    background-color: #3a3a3a;
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

/* Enhanced alert styling */
.alert {
    border-radius: 8px;
    border: 2px solid;
    padding: 1rem;
    margin: 1rem 0;
}

.alert-success {
    background-color: rgba(81, 207, 102, 0.1);
    border-color: #51cf66;
    color: #51cf66;
}

.alert-error {
    background-color: rgba(255, 107, 107, 0.1);
    border-color: #ff6b6b;
    color: #ff6b6b;
}

.alert-warning {
    background-color: rgba(255, 212, 59, 0.1);
    border-color: #ffd43b;
    color: #ffd43b;
}

.alert-info {
    background-color: rgba(51, 154, 240, 0.1);
    border-color: #339af0;
    color: #339af0;
}

/* Custom Searchable Dropdown for Branch */
.searchable-dropdown-container {
    position: relative;
    /* Tailwind w-full sm:w-64 applied in HTML */
}
.searchable-dropdown-list {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: #1c1c1c; /* Slightly lighter than body for dropdown */
    border: 1px solid #525252; /* border-neutral-600 */
    border-top: none;
    border-radius: 0 0 0.5rem 0.5rem; /* rounded-b-lg */
    max-height: 200px;
    overflow-y: auto;
    z-index: 50; /* Ensure it's above other content but below modals */
    /* Tailwind 'hidden' class toggled by JS */
}
.searchable-dropdown-item {
    padding: 0.75rem 1rem; /* p-3 px-4 */
    cursor: pointer;
    color: #d4d4d4; /* text-neutral-300 */
}
.searchable-dropdown-item:hover,
.searchable-dropdown-item.selected { /* 'selected' class added by JS */
    background-color: #404040; /* bg-neutral-700 */
    color: #fafafa; /* text-neutral-50 */
}
.searchable-dropdown-input { /* Tailwind classes applied in HTML */
    border-radius: 0.5rem; /* rounded-lg */
}
.searchable-dropdown-container.open .searchable-dropdown-input { /* 'open' class added by JS */
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

/* Custom select for consignee type in modal */
.custom-select-wrapper {
    position: relative;
}
.custom-select-wrapper select { /* Tailwind classes for base styling are in HTML */
    appearance: none;
    -webkit-appearance: none;
    /* SVG for dropdown arrow (Tailwind doesn't have a direct utility for this specific SVG background) */
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3E%3Cpath stroke='%239ca3af' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.75rem center; /* pr-10 equivalent for arrow positioning */
    background-size: 1.25em 1.25em; /* Adjust size as needed */
    padding-right: 2.5rem; /* Make space for arrow, Tailwind pr-10 */
}
